import { useCallback, useEffect, useRef } from "react";

import { ChatMessage } from "../services/chatService";

interface AutoMessageConfig {
  matchId: string;
  isEnabled: boolean;
  onAddMessage?: (message: ChatMessage) => void;
}

const SYSTEM_USER = {
  id: "system-auto",
  name: "<PERSON><PERSON> thống",
  avatar: "/ngoaihangtv.png",
  color: "#FF6B35",
};

const RANDOM_NAMES = [
  "MaxDowman",
  "CuongQuoc99",
  "VuotBien123",
  "<PERSON>bie12",
  "Crisseven7",
  "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON>ang<PERSON>ota88",
  "DTK09",
  "<PERSON><PERSON>ung<PERSON><PERSON>",
  "<PERSON>dq<PERSON><PERSON><PERSON><PERSON>",
  "David<PERSON><PERSON><PERSON>ue<PERSON>",
  "KingIbra10",
  "Son<PERSON>ro98",
  "Hieudz123",
  "LuanNguyen99",
  "Kaka2002",
  "<PERSON><PERSON><PERSON><PERSON>97",
  "<PERSON><PERSON><PERSON>e88",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>99",
  "VanVo2000",
  "<PERSON><PERSON><PERSON>123",
  "<PERSON><PERSON><PERSON><PERSON>09",
  "<PERSON>Khang98",
  "LamDz99",
  "<PERSON>rongT<PERSON>88",
  "<PERSON>hNien09",
  "CuongDepTrai",
  "HuuNghia97",
  "HNPro123",
  "Lequy99",
  "DatVipPro",
  "LongKen1999",
  "KhoiDepzai",
  "BaoTran123",
  "<PERSON>yHoang98",
  "TrungKien09",
  "ManhHung123",
  "VietAnh97",
  "NgocLan88",
  "AnhThu99",
  "KieuOanh97",
  "LanAnh2001",
  "QuynhTrang09",
  "MyLinh123",
  "HoaiThu99",
  "AnhTuan97",
  "SonTung09",
  "MinhVu123",
];

const JOIN_MESSAGES = ["đã tham gia phòng chat!"];

function getRandomName(): string {
  return RANDOM_NAMES[Math.floor(Math.random() * RANDOM_NAMES.length)];
}

function getRandomJoinMessage(): string {
  return JOIN_MESSAGES[Math.floor(Math.random() * JOIN_MESSAGES.length)];
}

function createMessage(isJoinMessage = false, isPromoMessage = false) {
  // Tạo timestamp ngẫu nhiên trong khoảng 0-30 giây trước thời điểm hiện tại
  // để tin nhắn tự động trộn lẫn với tin nhắn thật
  const randomDelay = Math.floor(Math.random() * 30000); // 0-30 seconds
  const mixedTimestamp = Date.now() - randomDelay;

  if (isPromoMessage) {
    return {
      id: `promo-${Date.now()}-${Math.random()}`,
      userId: "system-promo",
      userName: "NGOẠI HẠNG TV",
      userAvatar: "/ngoaihangtv.png",
      userColor: "#1e40af",
      message:
        "🔥 HOT chỉ có tại NGOẠI HẠNG TV - KUDV liên hệ CSKH để nhận ngay khuyến mãi nạp đầu lên tới 1688k. Quà tặng tân thủ lên tới 10 triệu. Đặc biệt Megalive hàng tuần quà tặng lên tới 288 triệu. ZALO: <a href='https://zalo.me/0994190627' target='_blank' rel='noopener noreferrer' style='color: #FFD700; text-decoration: underline; font-weight: bold;'>https://zalo.me/0994190627</a> - TELEGRAM: <a href='https://t.me/thuphuongepl' target='_blank' rel='noopener noreferrer' style='color: #FFD700; text-decoration: underline; font-weight: bold;'>https://t.me/thuphuongepl</a> 🎁",
      timestamp: mixedTimestamp,
      verified: true,
      reactions: {},
      isAutoMessage: true,
      isPromoMessage: true,
    };
  }

  const randomName = getRandomName();
  const content = isJoinMessage
    ? `${randomName} ${getRandomJoinMessage()}`
    : "đã tham gia phòng chat!";

  return {
    id: `msg_${Date.now()}_${Math.random()}`,
    userId: `user_${Math.random()}`,
    userName: randomName,
    userAvatar: "/ngoaihangtv.png",
    userColor: "bg-blue-500",
    message: content,
    timestamp: mixedTimestamp,
    verified: Math.random() > 0.7,
    reactions: {},
    isAutoMessage: true,
  };
}

export function useAutoMessages({
  matchId,
  isEnabled,
  onAddMessage,
}: AutoMessageConfig) {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const promoTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isActiveRef = useRef(false);

  // Function to add regular auto message to chat display
  const addAutoMessage = useCallback(() => {
    if (!isActiveRef.current || !onAddMessage) return;

    // Randomly choose between join message (40%) and regular message (60%)
    const isJoinMessage = Math.random() < 0.4;
    const newMessage = createMessage(isJoinMessage, false);

    onAddMessage(newMessage);

    // Schedule next message với random interval 1–3 phút
    if (isActiveRef.current) {
      const minInterval = 60000; // 1 phút
      const maxInterval = 180000; // 3 phút
      const nextInterval =
        Math.floor(Math.random() * (maxInterval - minInterval + 1)) +
        minInterval;
      timeoutRef.current = setTimeout(addAutoMessage, nextInterval);
    }
  }, [onAddMessage]);

  const addMixedMessage = useCallback(() => {
    if (!isActiveRef.current || !onAddMessage) return;

    const randomType = Math.random();
    let message;

    if (randomType < 0.2) {
      // 20% promo messages
      message = createMessage(false, true);
    } else if (randomType < 0.4) {
      // 20% join messages
      message = createMessage(true, false);
    } else {
      // 60% regular messages
      message = createMessage(false, false);
    }

    onAddMessage(message);

    // Schedule next mixed message with random interval between 1-5 minutes
    if (isActiveRef.current) {
      const minInterval = 60000; // 1 minute
      const maxInterval = 300000; // 5 minutes
      const randomInterval =
        Math.floor(Math.random() * (maxInterval - minInterval + 1)) +
        minInterval;
      promoTimeoutRef.current = setTimeout(addMixedMessage, randomInterval);
    }
  }, [onAddMessage]);

  // Start auto messages
  const startAutoMessages = useCallback(() => {
    if (!isEnabled || isActiveRef.current) return;

    isActiveRef.current = true;

    // Tin nhắn đầu tiên sau 1–3 phút
    const minDelay = 60000; // 1 phút
    const maxDelay = 180000; // 3 phút
    const randomDelay =
      Math.floor(Math.random() * (maxDelay - minDelay + 1)) + minDelay;

    timeoutRef.current = setTimeout(addAutoMessage, randomDelay);
  }, [isEnabled, addAutoMessage]);

  // Stop auto messages
  const stopAutoMessages = useCallback(() => {
    isActiveRef.current = false;

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // Effect to handle enable/disable
  useEffect(() => {
    if (isEnabled) {
      startAutoMessages();
      // Start mixed messages after 1-3 minutes
      const minDelay = 60000; // 1 phút
      const maxDelay = 180000; // 3 phút
      const randomDelay =
        Math.floor(Math.random() * (maxDelay - minDelay + 1)) + minDelay;
      promoTimeoutRef.current = setTimeout(addMixedMessage, randomDelay);
    } else {
      stopAutoMessages();
      if (promoTimeoutRef.current) {
        clearTimeout(promoTimeoutRef.current);
        promoTimeoutRef.current = null;
      }
    }

    return () => {
      stopAutoMessages();
      if (promoTimeoutRef.current) {
        clearTimeout(promoTimeoutRef.current);
        promoTimeoutRef.current = null;
      }
    };
  }, [isEnabled, startAutoMessages, stopAutoMessages, addMixedMessage]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopAutoMessages();
      if (promoTimeoutRef.current) {
        clearTimeout(promoTimeoutRef.current);
        promoTimeoutRef.current = null;
      }
    };
  }, [stopAutoMessages]);

  return {
    startAutoMessages,
    stopAutoMessages,
    isActive: isActiveRef.current,
  };
}
