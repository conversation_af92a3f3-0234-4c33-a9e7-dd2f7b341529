"use client";

import {
  ChatMessage as ChatMessageType,
  formatChatTime,
} from "@/services/chatService";

import { useState } from "react";

interface ChatMessageProps {
  message: ChatMessageType;
  isOwnMessage: boolean;
  onReply?: (message: ChatMessageType) => void;
  onReact?: (messageId: string, reactionType: string) => void;
  onPin?: (messageId: string, pinned: boolean) => void;
  onDelete?: (messageId: string) => void;
  isAdmin?: boolean;
  customTextClass?: string;
  customBackgroundClass?: string;
}

export default function ChatMessage({
  message,
  isOwnMessage,
  onReply,
  onReact,
  onPin,
  onDelete,
  isAdmin = false,
  customTextClass,
  customBackgroundClass,
}: ChatMessageProps) {
  const [showActions, setShowActions] = useState(false);

  // Generate random color for avatar if userColor is not provided
  const getRandomAvatarColor = (userName: string) => {
    const colors = [
      "bg-red-500",
      "bg-blue-500",
      "bg-green-500",
      "bg-yellow-500",
      "bg-purple-500",
      "bg-pink-500",
      "bg-indigo-500",
      "bg-teal-500",
      "bg-orange-500",
      "bg-cyan-500",
      "bg-lime-500",
      "bg-emerald-500",
      "bg-violet-500",
      "bg-fuchsia-500",
      "bg-rose-500",
      "bg-amber-500",
      "bg-sky-500",
      "bg-slate-500",
      "bg-gray-500",
      "bg-zinc-500",
    ];

    // Use userName to generate consistent color for same user
    let hash = 0;
    for (let i = 0; i < userName.length; i++) {
      hash = userName.charCodeAt(i) + ((hash << 5) - hash);
    }
    const index = Math.abs(hash) % colors.length;
    return colors[index];
  };

  const avatarColor =
    message.userColor || getRandomAvatarColor(message.userName || "User");

  const handleReply = () => {
    if (onReply) {
      onReply(message);
    }
    setShowActions(false);
  };

  const handleReact = (reactionType: string) => {
    if (onReact) {
      onReact(message.id, reactionType);
    }
    setShowActions(false);
  };

  const handlePin = () => {
    if (onPin) {
      onPin(message.id, !message.pinned);
    }
    setShowActions(false);
  };

  const handleDelete = () => {
    if (onDelete && confirm("Bạn có chắc muốn xóa tin nhắn này?")) {
      onDelete(message.id);
    }
    setShowActions(false);
  };

  const reactionEmojis = {
    like: "👍",
    love: "❤️",
    laugh: "😂",
    wow: "😮",
    sad: "😢",
    angry: "😠",
  };

  return (
    <div className={`group relative ${isOwnMessage ? "flex-row-reverse" : ""}`}>
      <div
        className={`flex items-start ${isOwnMessage ? "flex-row-reverse" : ""}`}
      >
        {/* Message Content */}
        <div
          className={`flex-1 min-w-0 max-w-[95%] ${
            isOwnMessage ? "text-right" : ""
          } ${customBackgroundClass || ""}`}
        >
          {/* Avatar, tên và nội dung chat trên cùng 1 hàng - có thể wrap */}
          <div
            className={`flex items-center space-x-2 ${
              isOwnMessage ? "flex-row-reverse space-x-reverse" : ""
            }`}
          >
            {/* Avatar nhỏ hơn */}
            <div className="relative flex-shrink-0">
              {message.userAvatar && message.userAvatar.startsWith("http") ? (
                <img
                  src={message.userAvatar}
                  alt={message.userName}
                  className="w-6 h-6 rounded-full object-cover"
                />
              ) : (
                <div
                  className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-semibold ${avatarColor}`}
                >
                  {message.userName?.charAt(0)?.toUpperCase() || "U"}
                </div>
              )}

              {/* Online indicator */}
              <div className="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
            </div>

            {/* User Name và Message Text - nằm cùng hàng, có thể wrap */}
            <div className="flex-1 min-w-0">
              <span className="font-semibold text-gray-500 dark:text-white text-sm">
                {message.userName}
                {message.verified && (
                  <span
                    className="ml-1 text-blue-500"
                    title="Tài khoản đã xác thực"
                  >
                    ✓
                  </span>
                )}
                {message.isAdmin && (
                  <span className="ml-1 text-red-500" title="Quản trị viên">
                    👑
                  </span>
                )}
              </span>

              {message.isPromoMessage ? (
                <span
                  className="text-sm break-words ml-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-3 py-2 rounded-lg shadow-lg border-l-4 border-yellow-400 font-medium animate-pulse"
                  dangerouslySetInnerHTML={{ __html: message.message }}
                />
              ) : (
                <span
                  className={`text-sm break-words ml-2 ${
                    isOwnMessage
                      ? "text-blue-500 dark:text-blue-400"
                      : "text-gray-300 dark:text-white"
                  } ${message.pinned ? "ring-2 ring-yellow-400" : ""}`}
                >
                  {message.message}
                </span>
              )}
            </div>
          </div>

          {/* Pinned indicator */}
          {message.pinned && (
            <div
              className={`mt-1 text-xs text-yellow-600 dark:text-yellow-400 ${
                isOwnMessage ? "text-right" : ""
              }`}
            >
              📌 Tin nhắn đã ghim
            </div>
          )}

          {/* Reply to message */}
          {/* {message.replyTo && (
            <div className={`mt-2 text-xs text-gray-500 dark:text-gray-400 ${isOwnMessage ? 'text-right' : ''}`}>
              <div className={`inline-block px-2 py-1 rounded bg-gray-50 dark:bg-gray-800 ${isOwnMessage ? 'text-right' : ''}`}>
                <span className="font-medium">Trả lời {message.replyTo.userName}:</span>
                <span className="ml-1">{message.replyTo.message}</span>
              </div>
            </div>
          )} */}

          {/* Reactions */}
          {/* {message.reactions && Object.keys(message.reactions).length > 0 && (
            <div className={`mt-2 flex items-center space-x-1 ${isOwnMessage ? 'justify-end' : ''}`}>
              {Object.entries(message.reactions).map(([type, count]) => (
                <span key={type} className="inline-flex items-center space-x-1 px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-xs">
                  <span>{reactionEmojis[type as keyof typeof reactionEmojis] || type}</span>
                  <span className="text-gray-600 dark:text-gray-300">{count}</span>
                </span>
              ))}
            </div>
          )} */}
        </div>
      </div>

      {/* Message Actions */}
      <div
        className={`absolute top-0 ${
          isOwnMessage ? "left-0" : "right-0"
        } opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10`}
      >
        <div
          className={`flex items-center space-x-1 ${
            isOwnMessage ? "flex-row-reverse" : ""
          }`}
        >
          {/* Reply Button */}
          {/* <button
            onClick={handleReply}
            className="p-1 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            title="Trả lời"
          >
            <svg className="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
            </svg>
          </button> */}

          {/* React Button */}
          {/* <button
            onClick={() => setShowActions(!showActions)}
            className="p-1 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            title="Biểu tượng cảm xúc"
          >
            <svg className="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </button> */}

          {/* Admin Actions */}
          {isAdmin && (
            <>
              {/* Pin/Unpin Button */}
              <button
                onClick={handlePin}
                className="p-1 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                title={message.pinned ? "Bỏ ghim" : "Ghim tin nhắn"}
              >
                <svg
                  className="w-4 h-4 text-gray-600 dark:text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"
                  />
                </svg>
              </button>

              {/* Delete Button */}
              <button
                onClick={handleDelete}
                className="p-1 rounded-full bg-red-100 dark:bg-red-900/20 hover:bg-red-200 dark:hover:bg-red-900/40 transition-colors"
                title="Xóa tin nhắn"
              >
                <svg
                  className="w-4 h-4 text-red-600 dark:text-red-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  />
                </svg>
              </button>
            </>
          )}
        </div>

        {/* Reaction Picker */}
        {showActions && (
          <div
            className={`absolute top-full mt-1 p-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 ${
              isOwnMessage ? "left-0" : "right-0"
            }`}
          >
            <div className="flex items-center space-x-2">
              {Object.entries(reactionEmojis).map(([type, emoji]) => (
                <button
                  key={type}
                  onClick={() => handleReact(type)}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
                  title={type}
                >
                  <span className="text-lg">{emoji}</span>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
