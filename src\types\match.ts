// Match Data Interface
export interface MatchData {
  id: string;
  league: string;
  category: string;
  homeTeam: {
    logo: string;
    name: string;
    score: number;
  };
  awayTeam: {
    logo: string;
    name: string;
    score: number;
  };
  cards: {
    redAway: number;
    redHome: number;
    yellowAway: number;
    yellowHome: number;
  };
  odds: {
    asian?: {
      away: string;
      home: string;
      handicap: string;
    };
    european?: {
      away: string;
      draw: string;
      home: string;
    };
    overUnder?: {
      line: string;
      over: string;
      under: string;
    };
  } | null;
  liveData: LiveDataItem[];
  liveTrack: string | null;
  typeMatch: string;
  status: string;
  date: string;
  time: string;
  links: string[];
  viewFake: number;
  liveFake: number;
  createdAt: string;
  updatedAt: string;
  parseData: {
    time?: string;
    status?: string;
  } | null;
  title: string | null;
  imageUrl: string | null;
  author: string | null;
  hashtags: string | null;
  timestamp: string | null;
  flv: FlvData | null;
  _ownLeague: boolean;
  _ownHomeTeam: boolean;
  _ownAwayTeam: boolean;
  _ownCards: boolean;
  _ownOdds: boolean;
  _ownLiveTrack: boolean;
  _ownStatus: boolean;
  _ownDate: boolean;
  _ownTime: boolean;
  _ownParseData: boolean;
  key_sync: string | null;
  incidents: IncidentData | null;
  statistics: StatisticsResponse[] | null;
  href?: string;
}

// Live Data Item Interface
export interface LiveDataItem {
  time?: string;
  event?: string;
  description?: string;
  blv?: string;
  flv?: string;
  hls?: string;
  [key: string]: unknown;
}

// Flv Data Interface
export interface FlvData {
  url?: string;
  quality?: string;
  [key: string]: unknown;
}

// Incident Data Interface
export interface IncidentData {
  cards?: {
    red?: number;
    yellow?: number;
  };
  goals?: {
    home?: number;
    away?: number;
  };
  [key: string]: unknown;
}

export interface StatisticsResponse {
  id: string;
  type: number; // 0 = Full match, 1 = First half, 2 = Second half
  stats: Array<{
    team_id: string;
    goals: number;
    shots: number;
    shots_on_target: number;
    shots_off_target: number;
    ball_possession: number;
    corner_kicks: number;
    yellow_cards: number;
    red_cards: number;
    attacks: number;
    dangerous_attack: number;
    passes: number;
    passes_accuracy: number;
    [key: string]: unknown;
  }>;
}

// Statistics Data Interface - Updated to match actual API response
export interface StatisticsData {
  possession?: {
    home?: number;
    away?: number;
  };
  shots?: {
    home?: number;
    away?: number;
  };
  shotsOnTarget?: {
    home?: number;
    away?: number;
  };
  goals?: {
    home?: number;
    away?: number;
  };
  cards?: {
    home?: number;
    away?: number;
  };
  yellowCards?: {
    home?: number;
    away?: number;
  };
  redCards?: {
    home?: number;
    away?: number;
  };
  corners?: {
    home?: number;
    away?: number;
  };
  attacks?: {
    home?: number;
    away?: number;
  };
  dangerousAttacks?: {
    home?: number;
    away?: number;
  };
  passes?: {
    home?: number;
    away?: number;
  };
}

// Legacy Statistics Data Interface (for future use if needed)
export interface LegacyStatisticsData {
  id: string;
  type: number;
  stats: Array<{
    team_id: string;
    goals: number;
    shots: number;
    shots_on_target: number;
    shots_off_target: number;
    ball_possession: number;
    passes: number;
    passes_accuracy: number;
    corner_kicks: number;
    yellow_cards: number;
    red_cards: number;
    attacks: number;
    dangerous_attack: number;
    [key: string]: unknown;
  }>;
}

// API Response Interface
export interface ApiResponse {
  data: MatchData[];
  total: number;
  page: number;
  limit: number;
}

// Fetch Parameters Interface
export interface FetchMatchesParams {
  category?: string;
  status?: string;
  typeMatch?: string;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: string;
  date?: string;
}

// Sports Category Mapping
export interface SportsCategoryMap {
  [key: string]: string;
}

// Status Filter Mapping
export interface StatusFilterMap {
  [key: string]: string;
}

export interface MatchStaticsResponse {
  id: string;
  type: number;
  stats: TeamStats[];
}

export interface TeamStats {
  team_id: string;
  goals: number;
  duels: number;
  duels_won: number;
  duels_failed: number;
  shots: number;
  shots_on_target: number;
  shots_off_target: number;
  blocked_shots: number;
  saves: number;
  passes: number;
  passes_accuracy: number;
  key_passes: number;
  long_balls: number;
  long_balls_accuracy: number;
  crosses: number;
  crosses_accuracy: number;
  dribble: number;
  dribble_succ: number;
  tackles: number;
  interceptions: number;
  clearances: number;
  offsides: number;
  freekicks: number;
  penalty: number;
  corner_kicks: number;
  throw_ins: number;
  substitution: number;
  yellow_cards: number;
  red_cards: number;

  // Attaque
  attacks: number;
  dangerous_attack: number;
  fastbreaks: number;
  fastbreak_shots: number;

  //
  punches: number;
  goalkeeper_strikes: number;
  goalkeeper_strikes_success: number;

  // Kiểm soát bóng
  ball_possession: number;
  poss_losts: number;

  [key: string]: unknown;
}
